/**
 * BAKASANA - Warm Colors Demo
 * Demonstracja nowych ciepłych, przyjaznych kolorów
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import Icon from '@/components/ui/Icon';
import OptimizedIcon from '@/components/OptimizedIcon';

export default function WarmColorsDemo() {
  return (
    <div className="min-h-screen bg-sanctuary p-8">
      <div className="max-w-6xl mx-auto space-y-12">
        
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-primary text-charcoal">
            🎨 Bakasana - Ciepłe Kolory
          </h1>
          <p className="text-lg text-stone max-w-2xl mx-auto">
            Nowa paleta ciepłych, przyjaznych kolorów dla bardziej ludzkiego doświadczenia
          </p>
        </div>

        {/* Color Palette Display */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="space-y-3">
            <div className="h-24 bg-cream rounded-lg shadow-sm"></div>
            <div className="text-center">
              <h3 className="font-medium text-charcoal">Cream</h3>
              <p className="text-sm text-stone">#F5F2ED</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="h-24 bg-warm-peach rounded-lg shadow-sm"></div>
            <div className="text-center">
              <h3 className="font-medium text-charcoal">Warm Peach</h3>
              <p className="text-sm text-stone">#F5D5C8</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="h-24 bg-soft-sage rounded-lg shadow-sm"></div>
            <div className="text-center">
              <h3 className="font-medium text-charcoal">Soft Sage</h3>
              <p className="text-sm text-stone">#9CAF88</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="h-24 bg-terracotta rounded-lg shadow-sm"></div>
            <div className="text-center">
              <h3 className="font-medium text-charcoal">Terracotta</h3>
              <p className="text-sm text-stone">#D4A574</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="h-24 bg-blush rounded-lg shadow-sm"></div>
            <div className="text-center">
              <h3 className="font-medium text-charcoal">Blush</h3>
              <p className="text-sm text-stone">#FFE5E5</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="h-24 bg-friendly-coral rounded-lg shadow-sm"></div>
            <div className="text-center">
              <h3 className="font-medium text-charcoal">Friendly Coral</h3>
              <p className="text-sm text-stone">#FF6B6B</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="h-24 bg-calm-lavender rounded-lg shadow-sm"></div>
            <div className="text-center">
              <h3 className="font-medium text-charcoal">Calm Lavender</h3>
              <p className="text-sm text-stone">#C7B8EA</p>
            </div>
          </div>
        </div>

        {/* Button Examples */}
        <div className="space-y-6">
          <h2 className="text-2xl font-primary text-charcoal text-center">
            Nowe Warianty Przycisków
          </h2>
          
          <div className="flex flex-wrap justify-center gap-4">
            <Button variant="peach">Peach Button</Button>
            <Button variant="coral">Coral Button</Button>
            <Button variant="sage">Sage Button</Button>
            <Button variant="terracotta">Terracotta Button</Button>
          </div>
        </div>

        {/* Text Color Examples */}
        <div className="space-y-6">
          <h2 className="text-2xl font-primary text-charcoal text-center">
            Kolory Tekstu
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <p className="text-cream text-lg">Tekst w kolorze Cream</p>
              <p className="text-warm-peach text-lg">Tekst w kolorze Warm Peach</p>
              <p className="text-soft-sage text-lg">Tekst w kolorze Soft Sage</p>
              <p className="text-terracotta text-lg">Tekst w kolorze Terracotta</p>
            </div>
            <div className="space-y-3">
              <p className="text-blush text-lg">Tekst w kolorze Blush</p>
              <p className="text-friendly-coral text-lg">Tekst w kolorze Friendly Coral</p>
              <p className="text-calm-lavender text-lg">Tekst w kolorze Calm Lavender</p>
            </div>
          </div>
        </div>

        {/* Icon Examples */}
        <div className="space-y-6">
          <h2 className="text-2xl font-primary text-charcoal text-center">
            Ikony z Nowymi Kolorami
          </h2>
          
          <div className="flex justify-center space-x-8">
            <OptimizedIcon name="heart" size="xl" color="coral" />
            <OptimizedIcon name="leaf" size="xl" color="softSage" />
            <OptimizedIcon name="sun" size="xl" color="peach" />
            <OptimizedIcon name="flower" size="xl" color="lavender" />
          </div>
        </div>

        {/* Usage Examples */}
        <div className="bg-whisper rounded-lg p-8 space-y-4">
          <h2 className="text-2xl font-primary text-charcoal">
            Jak Używać
          </h2>
          
          <div className="space-y-4 text-sm font-mono bg-pure-white p-4 rounded">
            <div>
              <span className="text-stone">// CSS Custom Properties</span><br/>
              <span className="text-enterprise-brown">background-color: var(--warm-peach);</span><br/>
              <span className="text-enterprise-brown">color: var(--friendly-coral);</span>
            </div>
            
            <div>
              <span className="text-stone">// Tailwind Classes</span><br/>
              <span className="text-enterprise-brown">className="bg-warm-peach text-friendly-coral"</span>
            </div>
            
            <div>
              <span className="text-stone">// Button Variants</span><br/>
              <span className="text-enterprise-brown">&lt;Button variant="coral"&gt;Click me&lt;/Button&gt;</span>
            </div>
            
            <div>
              <span className="text-stone">// Icon Colors</span><br/>
              <span className="text-enterprise-brown">&lt;Icon name="heart" color="coral" /&gt;</span>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
}
