'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

const MinimalistYogaHero = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    setIsLoaded(true);
    
    // Subtle parallax effect
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-sanctuary via-whisper to-sanctuary">
      {/* Background Image with Parallax */}
      <div 
        className="absolute inset-0 z-0"
        style={{
          transform: `translateY(${scrollY * 0.3}px)`,
        }}
      >
        <Image
          src="/images/background/bali-hero.webp"
          alt="BAKASANA - Retreaty jogi w Bali i Sri Lanka"
          fill
          className="object-cover object-center opacity-90"
          priority
          sizes="100vw"
          quality={90}
        />
        
        {/* Minimalist Overlay - Kremowy/Beżowy z przezroczystością */}
        <div className="absolute inset-0 bg-gradient-to-b from-sanctuary/60 via-sanctuary/40 to-sanctuary/70" />
        <div className="absolute inset-0 bg-gradient-to-r from-whisper/30 via-transparent to-whisper/30" />
      </div>

      {/* Main Content */}
      <div className={`relative z-20 text-center max-w-6xl mx-auto px-8 lg:px-12 transition-all duration-1000 ease-out ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-16'
      }`}>
        
        {/* Elegant Label */}
        <div className={`inline-flex items-center gap-3 px-8 py-3 mb-8 bg-pure-white/90 backdrop-blur-sm border border-charcoal/10 shadow-lg transition-all duration-700 delay-200 hover:bg-pure-white hover:shadow-xl ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
        }`}>
          <span className="w-2 h-2 bg-charcoal rectangular"></span>
          <span className="text-xs font-lato text-charcoal tracking-[0.3em] uppercase font-medium">
            RETREAT 2021 • Bali & Sri Lanka
          </span>
        </div>

        {/* Main Title - Old Money Style */}
        <h1 className={`font-didot font-light text-charcoal leading-[1.1] tracking-[0.3em] mb-6 transition-all duration-[2000ms] delay-500 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-5'
        }`}
        style={{
          fontSize: 'clamp(50px, 8vw, 90px)',
          textShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          BAKASANA
        </h1>

        {/* Subtitle - Old Money Style */}
        <p className={`font-helvetica font-light text-charcoal tracking-[0.15em] mb-8 transition-all duration-[1500ms] delay-1000 ${
          isLoaded ? 'opacity-70 translate-y-0' : 'opacity-0 translate-y-5'
        }`}
        style={{
          fontSize: '16px',
          marginTop: '40px'
        }}>
          jóga jest drogą ciszy
        </p>

        {/* Description */}
        <p className={`text-base md:text-lg lg:text-xl text-charcoal/70 max-w-4xl mx-auto leading-relaxed font-inter font-light mb-12 transition-all duration-1000 delay-500 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          Odkryj transformującą moc jogi w duchowych sercach Azji. Dołącz do naszej autentycznej podróży przez terasy ryżowe Ubud, świątynie Bali i tajemnicze krajobrazy Sri Lanki.
        </p>

        {/* Statistics - Minimalist Grid */}
        <div className={`grid grid-cols-2 md:grid-cols-4 gap-6 lg:gap-8 max-w-4xl mx-auto mb-12 transition-all duration-1000 delay-600 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          {[
            { number: '24+', label: 'miejsca' },
            { number: '10', label: 'lat doświadczenia' },
            { number: '500+', label: 'zadowolonych uczestników' },
            { number: 'A+', label: 'ocena satysfakcji' }
          ].map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="text-3xl md:text-4xl lg:text-5xl font-cormorant font-medium text-charcoal mb-2 group-hover:scale-105 transition-transform duration-300">
                {stat.number}
              </div>
              <div className="text-sm md:text-base text-charcoal/60 font-lato tracking-wide">
                {stat.label}
              </div>
            </div>
          ))}
        </div>

        {/* CTA Buttons - Minimalist Style */}
        <div className={`flex flex-col sm:flex-row gap-6 justify-center items-center transition-all duration-1000 delay-700 ${
          isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}>
          {/* Ghost Button */}
          <Link
            href="/program"
            className="group inline-flex items-center gap-3 px-8 py-4 bg-transparent text-charcoal border-2 border-charcoal/30 font-lato font-medium tracking-wide transition-all duration-400 hover:bg-charcoal hover:text-pure-white hover:border-charcoal hover:shadow-lg"
          >
            <span>Przegląd harmonogramu</span>
            <svg className="w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
            </svg>
          </Link>
          
          {/* Filled Button */}
          <Link
            href="/rezerwacja"
            className="group inline-flex items-center gap-3 px-8 py-4 bg-charcoal text-pure-white font-lato font-medium tracking-wide transition-all duration-400 hover:bg-charcoal/90 hover:shadow-lg hover:scale-105"
          >
            <span>Rezerwuj</span>
            <svg className="w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </div>

      {/* Subtle Floating Elements */}
      <div className="absolute inset-0 z-10 pointer-events-none overflow-hidden">
        <div 
          className="absolute top-1/4 left-1/4 w-3 h-3 bg-charcoal/20 rectangular"
          style={{ animation: 'float 6s ease-in-out infinite' }}
        />
        <div 
          className="absolute top-1/3 right-1/3 w-2 h-2 bg-charcoal/15 rectangular"
          style={{ animation: 'float 8s ease-in-out infinite', animationDelay: '2s' }}
        />
        <div 
          className="absolute bottom-1/3 left-1/3 w-2.5 h-2.5 bg-charcoal/10 rectangular"
          style={{ animation: 'float 7s ease-in-out infinite', animationDelay: '4s' }}
        />
      </div>

      {/* Scroll Indicator */}
      <div className={`absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20 transition-all duration-1000 delay-800 ${
        isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}>
        <div className="flex flex-col items-center text-charcoal/50">
          <span className="text-xs mb-3 font-lato tracking-[0.2em] uppercase font-light">
            Odkryj więcej
          </span>
          <div className="w-px h-8 bg-charcoal/20 animate-pulse"></div>
          <svg className="w-5 h-5 mt-2 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>
    </section>
  );
};

export default MinimalistYogaHero;